#!/bin/bash

echo "🔍 Cipher MCP Server Status Check"
echo "================================="

# Check if Cipher server is running
if pgrep -f "cipher.*mcp.*agent" > /dev/null; then
    echo "✅ Cipher MCP Server is running"
    echo "   Process ID: $(pgrep -f 'cipher.*mcp.*agent')"
else
    echo "❌ Cipher MCP Server is not running"
    echo "   Start it with: ./mcp_servers/cipher/test-local.sh"
fi

# Check Claude Desktop config
if [ -f "$HOME/Library/Application Support/Claude/claude_desktop_config.json" ]; then
    echo "✅ Claude Desktop configured"
    echo "   Config: $HOME/Library/Application Support/Claude/claude_desktop_config.json"
else
    echo "❌ Claude Desktop not configured"
fi

# Check other config files
echo ""
echo "📋 Available IDE Configurations:"
for config in mcp_servers/cipher/*-config.json; do
    if [ -f "$config" ]; then
        basename "$config" | sed 's/-config.json//' | sed 's/^/   ✅ /'
    fi
done

echo ""
echo "🚀 Next Steps:"
echo "1. ✅ Cipher server is running (if shown above)"
echo "2. ✅ Claude Desktop is configured"
echo "3. 🔄 Restart Claude Desktop to load the MCP server"
echo "4. 🧪 Test with: ask_cipher('Remember that Bryan is setting up MCP servers')"
echo ""
echo "For other IDEs:"
echo "- Cline: Copy mcp_servers/cipher/cline-config.json to VS Code MCP settings"
echo "- Cursor: Copy mcp_servers/cipher/cursor-vscode-config.json to Cursor MCP settings"
echo "- Others: Use the same JSON structure"
echo ""
echo "📖 Full guide: mcp_servers/cipher/IDE_SETUP_GUIDE.md"
