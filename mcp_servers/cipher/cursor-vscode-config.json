{"mcpServers": {"cipher": {"command": "cipher", "args": ["--mode", "mcp", "--agent", "/Users/<USER>/Documents/mcp_servers/cipher/cipher.yml"], "env": {"MCP_SERVER_MODE": "default", "OPENAI_API_KEY": "********************************************************************************************************************************************************************", "VECTOR_STORE_TYPE": "pinecone", "PINECONE_API_KEY": "pcsk_6VDwuL_PDZHbP2sgmi4iExvvnrMWbaX7xRcuiA6wPrgvUpp9bAghhXAX9e5p9mYDESB6YB", "PINECONE_INDEX_NAME": "cipher", "PINECONE_HOST": "https://cipher-zcdgm7a.svc.aped-4627-b74a.pinecone.io", "PINECONE_ENVIRONMENT": "us-east-1", "VECTOR_STORE_API_KEY": "pcsk_6VDwuL_PDZHbP2sgmi4iExvvnrMWbaX7xRcuiA6wPrgvUpp9bAghhXAX9e5p9mYDESB6YB", "VECTOR_STORE_INDEX": "cipher", "VECTOR_STORE_HOST": "https://cipher-zcdgm7a.svc.aped-4627-b74a.pinecone.io", "VECTOR_STORE_ENVIRONMENT": "us-east-1", "VECTOR_STORE_COLLECTION": "knowledge_memory", "REFLECTION_VECTOR_STORE_COLLECTION": "reflection_memory", "DISABLE_REFLECTION_MEMORY": "false"}}}}