#!/bin/bash

echo "🧪 Testing Milvus Connection - Simple Test"
echo "=========================================="

# Set API key
export OPENAI_API_KEY=********************************************************************************************************************************************************************

# Try username/password authentication (sometimes works better)
export MCP_SERVER_MODE=default
export VECTOR_STORE_TYPE=milvus
export VECTOR_STORE_URL="https://in03-f3783562188eb07.serverless.gcp-us-west1.cloud.zilliz.com:443"
export VECTOR_STORE_USERNAME="<EMAIL>"
export VECTOR_STORE_PASSWORD="#31#Brxsib"

# Clear token variables
unset VECTOR_STORE_TOKEN
unset VECTOR_STORE_API_KEY
unset MILVUS_TOKEN

export VECTOR_STORE_COLLECTION="knowledge_memory"
export REFLECTION_VECTOR_STORE_COLLECTION="reflection_memory"
export DISABLE_REFLECTION_MEMORY="false"

echo "✅ Configuration set:"
echo "   URL: $VECTOR_STORE_URL"
echo "   Username: $VECTOR_STORE_USERNAME"
echo "   Password: ${VECTOR_STORE_PASSWORD:0:3}***"
echo "   Auth method: Username/Password"

echo ""
echo "🚀 Testing Milvus connection..."
cipher --mode mcp --agent ./mcp_servers/cipher/cipher.yml
