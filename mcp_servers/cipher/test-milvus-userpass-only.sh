#!/bin/bash

echo "🧪 Testing Milvus with Username/Password ONLY"
echo "============================================="

# Set API key
export OPENAI_API_KEY=********************************************************************************************************************************************************************

# Milvus configuration - USERNAME/PASSWORD ONLY
export MCP_SERVER_MODE=default
export VECTOR_STORE_TYPE=milvus
export VECTOR_STORE_URL="https://in03-f3783562188eb07.serverless.gcp-us-west1.cloud.zilliz.com"
export VECTOR_STORE_USERNAME="<EMAIL>"
export VECTOR_STORE_PASSWORD="#31#Brxsib"

# CLEAR all token variables
unset VECTOR_STORE_TOKEN
unset VECTOR_STORE_API_KEY
unset MILVUS_TOKEN
unset MILVUS_API_KEY
unset PINECONE_API_KEY

export VECTOR_STORE_COLLECTION="knowledge_memory"
export REFLECTION_VECTOR_STORE_COLLECTION="reflection_memory"
export DISABLE_REFLECTION_MEMORY="false"

echo "✅ Configuration:"
echo "   URL: $VECTOR_STORE_URL"
echo "   Username: $VECTOR_STORE_USERNAME"
echo "   Password: ${VECTOR_STORE_PASSWORD:0:3}***"
echo "   Auth: Username/Password ONLY (no tokens)"

echo ""
echo "🚀 Testing Milvus with username/password only..."
cipher --mode mcp --agent ./mcp_servers/cipher/cipher.yml
