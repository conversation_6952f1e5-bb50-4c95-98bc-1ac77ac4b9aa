#!/bin/bash

echo "🧪 Testing NEW Milvus Token"
echo "==========================="

# Set API key
export OPENAI_API_KEY=********************************************************************************************************************************************************************

# Use the NEW token you provided
export MCP_SERVER_MODE=default
export VECTOR_STORE_TYPE=milvus
export VECTOR_STORE_URL="https://in03-f3783562188eb07.serverless.gcp-us-west1.cloud.zilliz.com"
export VECTOR_STORE_TOKEN="ee1c3403d16e8cec1fcb4ddda5dccb10cb7d3adfb95f273f050e2b7e7591d13a175f7c6ddb181cff31a421fd94a5bb2eeeccfddc"

# Clear other auth variables
unset VECTOR_STORE_USERNAME
unset VECTOR_STORE_PASSWORD
unset VECTOR_STORE_API_KEY

export VECTOR_STORE_COLLECTION="knowledge_memory"
export REFLECTION_VECTOR_STORE_COLLECTION="reflection_memory"
export DISABLE_REFLECTION_MEMORY="false"

echo "✅ Configuration:"
echo "   URL: $VECTOR_STORE_URL"
echo "   Token: ${VECTOR_STORE_TOKEN:0:20}..."
echo "   Auth: Token-based"

echo ""
echo "🚀 Testing with NEW Milvus token..."
cipher --mode mcp --agent ./mcp_servers/cipher/cipher.yml
