#!/bin/bash

echo "🌲 Setting up Cipher with Pinecone"
echo "=================================="

# Set API key
export OPENAI_API_KEY=********************************************************************************************************************************************************************

# Pinecone configuration
export MCP_SERVER_MODE=default
export VECTOR_STORE_TYPE=pinecone
export PINECONE_API_KEY="992a1afe1dce0d5996d6db0bf26fc800820c222785ec0e1c487829e21e134d46573ac197592a8075ba8e9af3e174f53fa0bf4c86"
export PINECONE_INDEX_NAME="cipher-memory"  # We'll create this index
export PINECONE_ENVIRONMENT="gcp-starter"   # Common for free tier

# Alternative environment variable names that Cipher might expect
export VECTOR_STORE_API_KEY="992a1afe1dce0d5996d6db0bf26fc800820c222785ec0e1c487829e21e134d46573ac197592a8075ba8e9af3e174f53fa0bf4c86"
export VECTOR_STORE_INDEX="cipher-memory"
export VECTOR_STORE_ENVIRONMENT="gcp-starter"

# Clear Milvus variables
unset VECTOR_STORE_URL
unset VECTOR_STORE_TOKEN
unset VECTOR_STORE_USERNAME
unset VECTOR_STORE_PASSWORD

export VECTOR_STORE_COLLECTION="knowledge_memory"
export REFLECTION_VECTOR_STORE_COLLECTION="reflection_memory"
export DISABLE_REFLECTION_MEMORY="false"

echo "✅ Pinecone Configuration:"
echo "   API Key: ${PINECONE_API_KEY:0:20}..."
echo "   Index: $PINECONE_INDEX_NAME"
echo "   Environment: $PINECONE_ENVIRONMENT"

echo ""
echo "🚀 Testing Pinecone connection..."
cipher --mode mcp --agent ./mcp_servers/cipher/cipher.yml
