#!/bin/bash

echo "🌲 Launching Cipher MCP Server with Pinecone"
echo "============================================"

# Check if API keys are set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "❌ OPENAI_API_KEY not set. Please run:"
    echo "export OPENAI_API_KEY=your_key"
    exit 1
fi

echo "✅ API keys are set"

# Check if already running
if pgrep -f "cipher.*mcp.*agent" > /dev/null; then
    echo "⚠️  Cipher server is already running (PID: $(pgrep -f 'cipher.*mcp.*agent'))"
    echo "   Kill it first with: pkill -f 'cipher.*mcp.*agent'"
    exit 1
fi

# Pinecone configuration
export MCP_SERVER_MODE=default
export VECTOR_STORE_TYPE=pinecone
export PINECONE_API_KEY="992a1afe1dce0d5996d6db0bf26fc800820c222785ec0e1c487829e21e134d46573ac197592a8075ba8e9af3e174f53fa0bf4c86"
export PINECONE_INDEX_NAME="cipher-memory"
export PINECONE_ENVIRONMENT="gcp-starter"

# Alternative variable names
export VECTOR_STORE_API_KEY="992a1afe1dce0d5996d6db0bf26fc800820c222785ec0e1c487829e21e134d46573ac197592a8075ba8e9af3e174f53fa0bf4c86"
export VECTOR_STORE_INDEX="cipher-memory"
export VECTOR_STORE_ENVIRONMENT="gcp-starter"

# Clear Milvus variables
unset VECTOR_STORE_URL
unset VECTOR_STORE_TOKEN
unset VECTOR_STORE_USERNAME
unset VECTOR_STORE_PASSWORD

export VECTOR_STORE_COLLECTION="knowledge_memory"
export REFLECTION_VECTOR_STORE_COLLECTION="reflection_memory"
export DISABLE_REFLECTION_MEMORY="false"

echo "✅ Pinecone configuration set"

# Launch Cipher
echo ""
echo "🚀 Starting Cipher MCP Server with Pinecone..."
echo "Configuration: ./mcp_servers/cipher/cipher.yml"
echo ""

cipher --mode mcp --agent ./mcp_servers/cipher/cipher.yml
