#!/bin/bash

echo "🌲 Launching Cipher MCP Server with CORRECT Pinecone Config"
echo "=========================================================="

# Check if API keys are set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "❌ OPENAI_API_KEY not set. Please run:"
    echo "export OPENAI_API_KEY=your_key"
    exit 1
fi

echo "✅ API keys are set"

# Check if already running
if pgrep -f "cipher.*mcp.*agent" > /dev/null; then
    echo "⚠️  Cipher server is already running (PID: $(pgrep -f 'cipher.*mcp.*agent'))"
    echo "   Kill it first with: pkill -f 'cipher.*mcp.*agent'"
    exit 1
fi

# CORRECT Pinecone configuration from your dashboard
export MCP_SERVER_MODE=default
export VECTOR_STORE_TYPE=pinecone

# Your actual Pinecone credentials
export PINECONE_API_KEY="pcsk_6VDwuL_PDZHbP2sgmi4iExvvnrMWbaX7xRcuiA6wPrgvUpp9bAghhXAX9e5p9mYDESB6YB"
export PINECONE_INDEX_NAME="cipher"
export PINECONE_HOST="https://cipher-zcdgm7a.svc.aped-4627-b74a.pinecone.io"
export PINECONE_ENVIRONMENT="us-east-1"

# Alternative variable names that Cipher might expect
export VECTOR_STORE_API_KEY="pcsk_6VDwuL_PDZHbP2sgmi4iExvvnrMWbaX7xRcuiA6wPrgvUpp9bAghhXAX9e5p9mYDESB6YB"
export VECTOR_STORE_INDEX="cipher"
export VECTOR_STORE_HOST="https://cipher-zcdgm7a.svc.aped-4627-b74a.pinecone.io"
export VECTOR_STORE_ENVIRONMENT="us-east-1"

# Clear any conflicting variables
unset VECTOR_STORE_URL
unset VECTOR_STORE_TOKEN
unset VECTOR_STORE_USERNAME
unset VECTOR_STORE_PASSWORD

export VECTOR_STORE_COLLECTION="knowledge_memory"
export REFLECTION_VECTOR_STORE_COLLECTION="reflection_memory"
export DISABLE_REFLECTION_MEMORY="false"

echo "✅ CORRECT Pinecone configuration set:"
echo "   Index: cipher"
echo "   Host: https://cipher-zcdgm7a.svc.aped-4627-b74a.pinecone.io"
echo "   Environment: us-east-1"
echo "   API Key: pcsk_6VDwuL_...***"

# Launch Cipher
echo ""
echo "🚀 Starting Cipher MCP Server with CORRECT Pinecone config..."
echo "Configuration: ./mcp_servers/cipher/cipher.yml"
echo ""

cipher --mode mcp --agent ./mcp_servers/cipher/cipher.yml
