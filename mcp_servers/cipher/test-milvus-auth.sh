#!/bin/bash

echo "🧪 Testing Different Milvus Authentication Methods"
echo "================================================="

# Check if API keys are set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "❌ OPENAI_API_KEY not set. Please run:"
    echo "export OPENAI_API_KEY=your_key"
    exit 1
fi

echo "✅ API keys are set"

# Test 1: Token-only authentication (most common for Zilliz Cloud)
echo ""
echo "🔧 Test 1: Token-only authentication"
export MCP_SERVER_MODE=default
export VECTOR_STORE_TYPE=milvus
export VECTOR_STORE_URL="https://in03-f3783562188eb07.serverless.gcp-us-west1.cloud.zilliz.com:443"
export VECTOR_STORE_TOKEN="2a0fb78ab31e101bfe7f72e98495cb107533bb810c6ff76f47960eaa9d4b7a72ec7ed4a41a4e643f89177373dde16c2282366798"

# Clear username/password
unset VECTOR_STORE_USERNAME
unset VECTOR_STORE_PASSWORD
unset VECTOR_STORE_API_KEY
unset MILVUS_USERNAME
unset MILVUS_PASSWORD

export VECTOR_STORE_COLLECTION="knowledge_memory"
export REFLECTION_VECTOR_STORE_COLLECTION="reflection_memory"
export DISABLE_REFLECTION_MEMORY="false"

echo "Configuration:"
echo "  URL: $VECTOR_STORE_URL"
echo "  Token: ${VECTOR_STORE_TOKEN:0:20}..."
echo "  Auth method: Token only"

echo ""
echo "🚀 Starting Cipher with token authentication..."
timeout 15 cipher --mode mcp --agent ./mcp_servers/cipher/cipher.yml

echo ""
echo "⏱️  Test 1 completed (or timed out after 15 seconds)"
