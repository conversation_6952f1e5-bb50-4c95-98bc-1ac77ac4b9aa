#!/bin/bash

echo "🧠 Starting Cipher MCP Server"
echo "============================="

# Set API key
export OPENAI_API_KEY=********************************************************************************************************************************************************************

# Check if already running
if pgrep -f "cipher.*mcp.*agent" > /dev/null; then
    echo "⚠️  Cipher server is already running (PID: $(pgrep -f 'cipher.*mcp.*agent'))"
    echo "   Kill it first with: pkill -f 'cipher.*mcp.*agent'"
    exit 1
fi

echo "🚀 Starting Cipher MCP Server with Pinecone..."
./mcp_servers/cipher/launch-pinecone-correct.sh
